# ==============================================
# OMI FRONTEND ENVIRONMENT VARIABLES
# ==============================================
# Copy this file to .env.template and fill in your actual values

# NEXT.JS CONFIGURATION
NEXT_PUBLIC_NODE_ENV=development
NODE_ENV=development

# API CONFIGURATION  
API_URL=https://backend-hhibjajaja-uc.a.run.app
WEB_URL=https://app.memorion.me

# FIREBASE CONFIGURATION
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
NEXT_PUBLIC_FIREBASE_APP_ID=1:123456789:web:abcdef123456
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-XXXXXXXXXX

# REDIS CONFIGURATION
REDIS_DB_HOST=localhost
REDIS_DB_PORT=6379
REDIS_DB_PASSWORD=your_redis_password_here

# ALGOLIA SEARCH CONFIGURATION
NEXT_PUBLIC_ALGOLIA_APP_ID=your_algolia_app_id
NEXT_PUBLIC_ALGOLIA_API_KEY=your_algolia_search_api_key  
NEXT_PUBLIC_ALGOLIA_INDEX_NAME=memories

# CUSTOMER SUPPORT
NEXT_PUBLIC_GLEAP_API_KEY=your_gleap_api_key
NEXT_PUBLIC_ENABLE_GLEAP=true

# ADMIN & SECURITY
ADMIN_KEY=your_super_secret_admin_key