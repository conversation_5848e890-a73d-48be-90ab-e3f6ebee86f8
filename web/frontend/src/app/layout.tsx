import type { Metada<PERSON> } from 'next';
import { Mulish } from 'next/font/google';
import './globals.css';
import AppHeader from '../components/shared/app-header';
import Footer from '../components/shared/footer';
import envConfig from '../constants/envConfig';
import { GleapInit } from '@/src/components/shared/gleap';
import { GoogleAnalytics } from '@/src/components/shared/google-analytics';

const inter = Mulish({
  subsets: ['latin'],
  weight: ['200', '400', '500', '600', '700'],
  style: ['italic', 'normal'],
});

export const metadata: Metadata = {
  title: {
    default: 'Omi',
    template: '%s | Omi',
  },
  metadataBase: new URL(envConfig.WEB_URL),
  description: 'Open-source AI wearable Build using the power of recall',
  openGraph: {
    title: 'Omi',
    description: 'Open-source AI wearable Build using the power of recall',
    images: [
      {
        url: `${envConfig.WEB_URL}/favicon.jpg`,
        width: 256,
        height: 256,
        alt: 'Omi Logo',
      },
    ],
    type: 'website',
    siteName: 'Omi',
  },
  twitter: {
    card: 'summary',
    title: 'Omi',
    description: 'Open-source AI wearable Build using the power of recall',
    images: [`${envConfig.WEB_URL}/favicon.jpg`],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AppHeader />
        <main className="flex min-h-screen flex-col">
          <div className="w-full flex-grow">{children}</div>
        </main>
        <Footer />
      </body>
      <GleapInit />
      <GoogleAnalytics />
    </html>
  );
}
